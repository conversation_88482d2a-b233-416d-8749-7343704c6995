# ⚛️ TanStack Frontend Boilerplate

A modular and scalable React frontend starter using **TanStack tools**, built for modern, high-performance web apps.

---

## 🧰 Tech Stack

- **React** – UI library
- **Vite** – Lightning-fast dev environment
- **TanStack Router** – Type-safe, flexible client-side routing
- **TanStack Query** – Powerful async server state management
- **Axios** – For REST API integration
- **TanStack Table / Virtual / Form** *(optional)* – For complex UIs (tables, infinite scroll, forms)
- **Tailwind CSS** *(optional)* – Utility-first styling

---

## 🚀 Features

- 🔗 Type-safe nested routing with TanStack Router
- ⚡ Smart server data fetching, caching, and refetching with React Query
- ✅ Clean component structure with scalable folder organization
- 🧩 Optional support for:
  - Headless tables (TanStack Table)
  - Virtualized lists (TanStack Virtual)
  - Forms with validation (TanStack Form)
- 🎨 Optional Tailwind CSS styling support
- 🛠️ Built with developer experience and extensibility in mind

---

## 📁 Project Structure

```
tanstack-app/
├── src/
│   ├── api/              # Axios instance
│   ├── pages/            # Route components
│   ├── routes.jsx        # TanStack Router config
│   ├── App.jsx           # Main layout component
│   └── main.jsx          # App entry point
├── index.html
└── package.json
```

---

## 🛠️ Getting Started

```bash
# Create project using Vite
npm create vite@latest tanstack-app --template react

cd tanstack-app

# Install dependencies
npm install @tanstack/react-query @tanstack/react-router axios

# Optional: Tailwind CSS
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p

# Run dev server
npm run dev
```

---

## ✨ Example Features to Build

- Blog app with post list, post detail, and comment fetch
- Admin dashboard with sortable & filterable data tables
- Paginated & virtualized user list
- Type-safe forms with validation

---

## 💡 Tips & Best Practices

- Use `QueryClientProvider` once at root level
- Use `Link` from TanStack Router, not React Router
- Split API logic (Axios) from components
- Co-locate queries with components for reusability
- Add devtools for React Query in dev mode

---

## 📚 Resources

- [TanStack Query Docs](https://tanstack.com/query/latest)
- [TanStack Router Docs](https://tanstack.com/router/latest)
- [TanStack Table Docs](https://tanstack.com/table/latest)
- [Vite Docs](https://vitejs.dev/)
- [Tailwind CSS Docs](https://tailwindcss.com/)

---

## 📌 Note

This project does **not** use a backend framework like Next.js or Remix. It's a purely frontend stack. You can integrate this with any API backend (Node.js, Laravel, Django, etc.) as needed.

---

## 🧑‍💻 Author

Built with ❤️ by [Your Name](https://github.com/your-username)

---

## 📄 License

MIT
